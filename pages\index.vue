<template>
	<view class="scm-home-container">
		<!-- 顶部欢迎信息 -->
		<view class="welcome-section">
			<view class="welcome-card">
				<view class="welcome-header">
										<view class="greeting">
						<text class="greeting-text">{{ getGreeting() }}，{{ getUserDisplayName() }} 👋</text>
						<view class="date-info">
							<text class="date-text">{{ currentDate }}</text>
							<text class="task-info">，当前有 <text class="task-count">{{ pendingTasksCount }}</text> 项待处理任务</text>
						</view>
					</view>
					<view class="avatar-section">
						<image class="user-avatar" :src="userInfo.avatar || '/static/avatar-default.png'" mode="aspectFill"></image>
					</view>
				</view>
				
				<!-- 状态提醒卡片 -->
				<view class="status-cards">
					<view class="status-item">
						<view class="status-icon pending-orders">
							<text class="cuIcon-file-text"></text>
				</view>
						<view class="status-content">
							<text class="status-label">待处理订单</text>
							<text class="status-value">{{ statusData.pendingOrders }} 个</text>
						</view>
					</view>
					<view class="status-item">
						<view class="status-icon inventory-warning">
							<text class="cuIcon-warn"></text>
						</view>
						<view class="status-content">
							<text class="status-label">库存预警</text>
							<text class="status-value">{{ statusData.inventoryWarning }} 项</text>
						</view>
					</view>
					<view class="status-item">
						<view class="status-icon approval-pending">
							<text class="cuIcon-check"></text>
						</view>
						<view class="status-content">
							<text class="status-label">待审批</text>
							<text class="status-value">{{ statusData.approvalPending }} 项</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 业务数据统计卡片 -->
		<view class="metrics-section">
			<view class="metrics-grid">
				<!-- 销售订单 -->
				<view class="metric-card sales-card">
					<view class="metric-header">
						<view class="metric-icon sales-icon">
							<text class="cuIcon-cart"></text>
					</view>
						<view class="metric-info">
							<text class="metric-title">销售订单</text>
							<text class="metric-tag">今日</text>
					</view>
					</view>
					<view class="metric-content">
						<text class="metric-amount">¥{{ formatAmount(todayData.saleAmount) }}</text>
						<text class="metric-count">{{ todayData.saleCount }} 个订单</text>
					</view>
					</view>

				<!-- 出库订单 -->
				<view class="metric-card outbound-card">
					<view class="metric-header">
						<view class="metric-icon outbound-icon">
							<text class="cuIcon-deliver"></text>
					</view>
						<view class="metric-info">
							<text class="metric-title">出库订单</text>
							<text class="metric-tag">今日</text>
					</view>
					</view>
					<view class="metric-content">
						<text class="metric-amount">¥{{ formatAmount(todayData.outStorageAmount) }}</text>
						<text class="metric-count">{{ todayData.outStorageCount }} 个订单</text>
		</view>
	</view>

				<!-- 采购订单 -->
				<view class="metric-card purchase-card">
					<view class="metric-header">
						<view class="metric-icon purchase-icon">
							<text class="cuIcon-truck"></text>
						</view>
						<view class="metric-info">
							<text class="metric-title">采购订单</text>
							<text class="metric-tag">今日</text>
						</view>
					</view>
					<view class="metric-content">
						<text class="metric-amount">¥{{ formatAmount(todayData.purchaseAmount) }}</text>
						<text class="metric-count">{{ todayData.purchaseCount }} 个订单</text>
					</view>
				</view>

				<!-- 生产订单 -->
				<view class="metric-card production-card">
					<view class="metric-header">
						<view class="metric-icon production-icon">
							<text class="cuIcon-settings"></text>
						</view>
						<view class="metric-info">
							<text class="metric-title">生产订单</text>
							<text class="metric-tag">今日</text>
						</view>
					</view>
					<view class="metric-content">
						<text class="metric-amount">¥{{ formatAmount(todayData.mfgAmount) }}</text>
						<text class="metric-count">{{ todayData.mfgCount }} 个订单</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 快捷操作区 -->
		<view class="quick-actions-section">
			<view class="section-header">
				<text class="section-title">快捷操作</text>
				<text class="section-more" @click="handleMoreActions">更多 ></text>
			</view>
			<view class="quick-actions-grid">
				<view class="quick-action-item" @click="handleQuickAction('sales')">
					<view class="action-icon sales-bg">
						<text class="cuIcon-cart"></text>
					</view>
					<text class="action-text">销售订单</text>
				</view>
				<view class="quick-action-item" @click="handleQuickAction('purchase')">
					<view class="action-icon purchase-bg">
						<text class="cuIcon-truck"></text>
					</view>
					<text class="action-text">采购订单</text>
				</view>
				<view class="quick-action-item" @click="handleQuickAction('production')">
					<view class="action-icon production-bg">
						<text class="cuIcon-settings"></text>
					</view>
					<text class="action-text">生产计划</text>
				</view>
				<view class="quick-action-item" @click="handleQuickAction('inventory')">
					<view class="action-icon inventory-bg">
						<text class="cuIcon-goods"></text>
					</view>
					<text class="action-text">库存查询</text>
				</view>
			</view>
		</view>

		<!-- 待处理任务提醒 -->
		<view class="pending-tasks-section">
			<view class="section-header">
				<text class="section-title">待处理任务</text>
				<text class="section-more" @click="handleViewAllTasks">查看全部</text>
			</view>
			<view class="pending-tasks-list">
				<view class="task-item" v-for="(task, index) in pendingTasks" :key="index" @click="handleTaskClick(task)">
					<view class="task-icon" :class="task.iconClass">
						<text :class="task.icon"></text>
					</view>
					<view class="task-content">
						<text class="task-title">{{ task.title }}</text>
						<text class="task-desc">{{ task.description }}</text>
						<text class="task-time">{{ task.time }}</text>
					</view>
					<view class="task-actions">
						<view class="task-btn primary" @click.stop="handleTaskAction(task, 'handle')">
							{{ task.actionText }}
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 数据图表区 -->
		<view class="charts-section">
			<view class="section-header">
				<text class="section-title">业务趋势</text>
				<view class="chart-tabs">
					<text class="chart-tab" :class="{ active: activeChartTab === 'sales' }" @click="switchChartTab('sales')">销售数据</text>
					<text class="chart-tab" :class="{ active: activeChartTab === 'production' }" @click="switchChartTab('production')">生产数据</text>
				</view>
			</view>
			
			<!-- 销售数据图表 -->
			<view class="chart-container" v-show="activeChartTab === 'sales'">
				<view class="chart-card">
					<view class="chart-header">
						<text class="chart-title">销售数据趋势</text>
						<view class="chart-period-tabs">
							<text class="period-tab" :class="{ active: salesPeriod === '7' }" @click="changeSalesPeriod('7')">7天</text>
							<text class="period-tab" :class="{ active: salesPeriod === '30' }" @click="changeSalesPeriod('30')">30天</text>
						</view>
					</view>
					<view class="chart-content">
						<l-echart ref="salesChart" class="chart-canvas" @finished="initSalesChart"></l-echart>
					</view>
					<view class="chart-summary">
						<view class="summary-item">
							<text class="summary-label">总销售额</text>
							<text class="summary-value">¥{{ formatAmount(chartSummary.totalSales) }}</text>
						</view>
						<view class="summary-item">
							<text class="summary-label">订单数</text>
							<text class="summary-value">{{ chartSummary.totalOrders }}</text>
						</view>
						<view class="summary-item">
							<text class="summary-label">同比增长</text>
							<text class="summary-value growth-positive">+{{ chartSummary.growthRate }}%</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 生产数据图表 -->
			<view class="chart-container" v-show="activeChartTab === 'production'">
				<view class="chart-card">
					<view class="chart-header">
						<text class="chart-title">生产数据趋势</text>
						<view class="chart-period-tabs">
							<text class="period-tab" :class="{ active: productionPeriod === '7' }" @click="changeProductionPeriod('7')">7天</text>
							<text class="period-tab" :class="{ active: productionPeriod === '30' }" @click="changeProductionPeriod('30')">30天</text>
						</view>
					</view>
					<view class="chart-content">
						<l-echart ref="productionChart" class="chart-canvas" @finished="initProductionChart"></l-echart>
					</view>
					<view class="chart-summary">
						<view class="summary-item">
							<text class="summary-label">总产量</text>
							<text class="summary-value">{{ chartSummary.totalProduction }}吨</text>
						</view>
						<view class="summary-item">
							<text class="summary-label">合格率</text>
							<text class="summary-value">{{ chartSummary.qualityRate }}%</text>
						</view>
						<view class="summary-item">
							<text class="summary-label">完成率</text>
							<text class="summary-value">{{ chartSummary.completionRate }}%</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 功能模块区 -->
		<view class="function-modules-section">
			<view class="section-header">
				<text class="section-title">功能模块</text>
				<text class="section-more" @click="handleAllModules">全部 ></text>
			</view>
			<view class="modules-grid">
				<view class="module-item" v-for="(module, index) in functionModules" :key="index" @click="handleModuleClick(module)">
					<view class="module-icon" :class="module.bgClass">
						<text :class="module.icon"></text>
					</view>
					<text class="module-text">{{ module.name }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import storage from '@/utils/storage'
	import {
		orderChart
	} from '@/api/dashboard/analysis'
	import { 
		getDailyOrderPageApi
	} from '@/api/scm/stat/dailyorder'
	import { getUserProfile } from '@/api/system/user'
	
	// ECharts导入 - 适配不同平台
	// #ifdef H5
	import * as echarts from 'echarts'
	// #endif
	// #ifdef MP
	const echarts = require('../uni_modules/lime-echart/static/echarts.min.js')
	// #endif

	export default {
		data() {
			return {
				loading: false,
				// 用户信息
				userInfo: {
					id: null,
					username: '',
					nickname: '用户',
					avatar: '/static/avatar-default.png',
					email: '',
					mobile: '',
					dept: null
				},
				// 当前日期
				currentDate: '',
				// 待处理任务数量
				pendingTasksCount: 0,
				// 状态数据 - 基于真实接口数据
				statusData: {
					pendingOrders: 20, // pendingMfgCount + pendingOutCount = 10 + 10
					inventoryWarning: 9, // pendingInCount
					approvalPending: 2 // 保持原值，或可从其他字段获取
				},
				// 今日业务数据 - 参考真实接口数据结构
				todayData: {
					saleAmount: 800000, // 销售金额(分)
					saleCount: 10, // 销售订单数
					outStorageAmount: 500000, // 出库金额(分)
					outStorageCount: 9, // 出库订单数
					purchaseAmount: 50000, // 采购金额(分)
					purchaseCount: 20, // 采购订单数
					mfgAmount: 60000, // 生产金额(分)
					mfgCount: 10 // 生产订单数
				},
				// 待处理任务列表 - 基于真实数据
				pendingTasks: [
					{
						id: 1,
						title: '待入库提醒',
						description: '有9个入库订单等待处理，待入库数量: 5吨',
						time: '10分钟前',
						icon: 'cuIcon-warn',
						iconClass: 'task-icon-warning',
						actionText: '处理入库',
						type: 'inventory'
					},
					{
						id: 2,
						title: '生产任务',
						description: `有10个生产订单待处理，预计生产数量: 50吨`,
						time: '1小时前',
						icon: 'cuIcon-time',
						iconClass: 'task-icon-info',
						actionText: '安排生产',
						type: 'production'
					},
					{
						id: 3,
						title: '出库订单',
						description: `有10个出库订单等待处理，待出库数量: 30吨`,
						time: '2小时前',
						icon: 'cuIcon-deliver',
						iconClass: 'task-icon-info',
						actionText: '处理出库',
						type: 'outbound'
					},
					{
						id: 4,
						title: '审批提醒',
						description: '有2份申请单等待您的审批',
						time: '3小时前',
						icon: 'cuIcon-check',
						iconClass: 'task-icon-success',
						actionText: '去审批',
						type: 'approval'
					}
				],
				// 功能模块
				functionModules: [
					{
						id: 1,
						name: '客户管理',
						icon: 'cuIcon-group',
						bgClass: 'module-purple',
						route: '/pages/biz/customer/index'
					},
					{
						id: 2,
						name: '供应商管理',
						icon: 'cuIcon-deliver',
						bgClass: 'module-green',
						route: '/pages/biz/supplier/index'
					},
					{
						id: 3,
						name: '产品管理',
						icon: 'cuIcon-goods',
						bgClass: 'module-blue',
						route: '/pages/biz/product/index'
					},
					{
						id: 4,
						name: '报表中心',
						icon: 'cuIcon-chart',
						bgClass: 'module-orange',
						route: '/pages/biz/report/index'
					},
					{
						id: 5,
						name: '生产管理',
						icon: 'cuIcon-settings',
						bgClass: 'module-teal',
						route: '/pages/biz/production/index'
					},
					{
						id: 6,
						name: '仓库管理',
						icon: 'cuIcon-warehouse',
						bgClass: 'module-indigo',
						route: '/pages/biz/warehouse/index'
					},
					{
						id: 7,
						name: '质量管理',
						icon: 'cuIcon-medal',
						bgClass: 'module-pink',
						route: '/pages/biz/quality/index'
					},
					{
						id: 8,
						name: '系统设置',
						icon: 'cuIcon-settings',
						bgClass: 'module-gray',
						route: '/pages/system/index'
					}
				],
				// 图表相关数据
				activeChartTab: 'sales', // 当前激活的图表标签
				salesPeriod: '7', // 销售数据周期
				productionPeriod: '7', // 生产数据周期
				salesChart: null, // 销售图表实例
				productionChart: null, // 生产图表实例
				// 图表数据
				chartData: {
					sales: [], // 销售数据
					production: [] // 生产数据
				},
				// 图表汇总数据 - 基于真实接口数据计算
				chartSummary: {
					totalSales: 7800000, // 两天销售额总和: 7000000 + 800000
					totalOrders: 20, // 两天订单数总和: 10 + 10
					growthRate: -88.6, // 增长率: (800000 - 7000000) / 7000000 * 100
					totalProduction: 130, // 两天生产数量总和: 90 + 40
					qualityRate: 98.6, // 保持模拟数据
					completionRate: 92.3 // 保持模拟数据
				}
			}
		},

		computed: {
			// 计算待处理任务总数
			computedPendingTasksCount() {
				return this.statusData.pendingOrders + this.statusData.inventoryWarning + this.statusData.approvalPending
			}
		},

		mounted() {
			this.initData();
			this.loadUserInfo();
			this.loadData();
		},

		beforeDestroy() {
			// 销毁图表实例以避免内存泄漏
			if (this.salesChart) {
				this.salesChart.dispose();
				this.salesChart = null;
			}
			if (this.productionChart) {
				this.productionChart.dispose();
				this.productionChart = null;
			}
		},

		methods: {
			// 初始化数据
			initData() {
				// 设置当前日期
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0');
				const day = String(now.getDate()).padStart(2, '0');
				const weekDay = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][now.getDay()];
				this.currentDate = `${year}年${month}月${day}日 ${weekDay}`;
				
				// 计算待处理任务数量
				this.pendingTasksCount = this.computedPendingTasksCount;
			},

			// 获取用户信息
			async loadUserInfo() {
				try {
					const result = await getUserProfile();
					if (result && result.data) {
						this.userInfo = {
							id: result.data.id,
							username: result.data.username || '',
							nickname: result.data.nickname || result.data.username || '用户',
							avatar: result.data.avatar || '/static/avatar-default.png',
							email: result.data.email || '',
							mobile: result.data.mobile || '',
							dept: result.data.dept || null
						};
					}
				} catch (error) {
					console.error('获取用户信息失败:', error);
					// 使用默认用户信息
					this.userInfo.nickname = '用户';
				}
			},

			// 获取问候语
			getGreeting() {
				const hour = new Date().getHours();
				if (hour < 6) {
					return '凌晨好';
				} else if (hour < 9) {
					return '早上好';
				} else if (hour < 12) {
					return '上午好';
				} else if (hour < 14) {
					return '中午好';
				} else if (hour < 17) {
					return '下午好';
				} else if (hour < 19) {
					return '傍晚好';
				} else {
					return '晚上好';
				}
			},

			// 获取用户显示名称
			getUserDisplayName() {
				if (this.userInfo.nickname) {
					return this.userInfo.nickname;
				} else if (this.userInfo.username) {
					return this.userInfo.username;
				} else {
					return '用户';
				}
			},

			// 格式化金额显示
			formatAmount(amount) {
				if (!amount || amount === 0) return '0';
				// 将分转换为万元
				const yuan = amount / 100;
				if (yuan >= 10000) {
					return (yuan / 10000).toFixed(1) + '万';
				}
				return yuan.toLocaleString();
			},

			// 加载数据
			async loadData() {
				this.loading = true;
				try {
					// 这里可以调用实际的API接口
					// 目前使用模拟数据
					await this.loadTodayData();
					await this.loadPendingTasks();
					await this.loadChartData();

					// 数据加载完成后，延迟初始化图表
					this.$nextTick(() => {
						setTimeout(() => {
							this.initChartsAfterDataLoaded();
						}, 300); // 给图表组件一些时间完成渲染
					});
				} catch (error) {
					console.error('加载数据失败:', error);
					uni.showToast({
						title: '数据加载失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},

			// 加载今日数据
			async loadTodayData() {
				try {
					// 获取今日数据
					const today = new Date().toISOString().split('T')[0];
					const params = {
						pageNo: 1,
						pageSize: 1,
						date: today
					};

					const result = await getDailyOrderPageApi(params);
					
					if (result && result.data && result.data.list && result.data.list.length > 0) {
						const todayStats = result.data.list[0];
						console.log('获取到今日统计数据:', todayStats);
						
						// 更新今日数据
						this.todayData = {
							saleAmount: todayStats.saleAmount || 0,
							saleCount: todayStats.saleCount || 0,
							outStorageAmount: todayStats.outStorageAmount || 0,
							outStorageCount: todayStats.outStorageCount || 0,
							purchaseAmount: todayStats.purchaseAmout || 0, // 注意PC端拼写错误
							purchaseCount: todayStats.purchaseCount || 0,
							mfgAmount: todayStats.mfgAmount || 0,
							mfgCount: todayStats.mfgCount || 0
						};

						// 更新状态数据
						this.statusData = {
							pendingOrders: (todayStats.pendingMfgCount || 0) + (todayStats.pendingOutCount || 0),
							inventoryWarning: todayStats.pendingInCount || 0, // 使用真实的待入库数据
							approvalPending: 2   // 可以从其他字段获取
						};

						// 重新计算待处理任务数量
						this.pendingTasksCount = this.computedPendingTasksCount;

						// 更新待处理任务描述
						this.updatePendingTasksDescription(todayStats);
					} else {
						console.log('今日暂无统计数据，使用模拟数据');
					}

				} catch (error) {
					console.error('获取今日数据失败:', error);
					// 如果API调用失败，使用模拟数据
					console.log('使用模拟数据');
				}
			},

			// 加载待处理任务
			async loadPendingTasks() {
				try {
					// 待处理任务数据已经在loadTodayData中获取
					// 这里保持现有的模拟数据结构，实际项目中应根据API返回结构调整
				} catch (error) {
					console.error('获取待处理任务失败:', error);
					// 如果API调用失败，使用模拟数据
				}
			},

			// 更新待处理任务描述
			updatePendingTasksDescription(todayStats) {
				if (!todayStats) return;

				// 更新待处理任务列表
				this.pendingTasks = [
					{
						id: 1,
						title: '待入库提醒',
						description: `有${todayStats.pendingInCount || 0}个入库订单等待处理，待入库数量: ${todayStats.pendingInQuantity || 0}吨`,
						time: '10分钟前',
						icon: 'cuIcon-warn',
						iconClass: 'task-icon-warning',
						actionText: '处理入库',
						type: 'inventory'
					},
					{
						id: 2,
						title: '生产任务',
						description: `有${todayStats.pendingMfgCount || 0}个生产订单待处理，预计生产数量: ${todayStats.pendingMfgQuantity || 0}吨`,
						time: '1小时前',
						icon: 'cuIcon-time',
						iconClass: 'task-icon-info',
						actionText: '安排生产',
						type: 'production'
					},
					{
						id: 3,
						title: '出库订单',
						description: `有${todayStats.pendingOutCount || 0}个出库订单等待处理，待出库数量: ${todayStats.pendingOutQuantity || 0}吨`,
						time: '2小时前',
						icon: 'cuIcon-deliver',
						iconClass: 'task-icon-info',
						actionText: '处理出库',
						type: 'outbound'
					},
					{
						id: 4,
						title: '审批提醒',
						description: '有2份申请单等待您的审批',
						time: '3小时前',
						icon: 'cuIcon-check',
						iconClass: 'task-icon-success',
						actionText: '去审批',
						type: 'approval'
					}
				];

				console.log('待处理任务已更新:', this.pendingTasks);
			},

			// 快捷操作点击
			handleQuickAction(type) {
				const routes = {
					sales: '/pages/biz/sale/index',
					purchase: '/pages/biz/purchase/index', 
					production: '/pages/biz/production/index',
					inventory: '/pages/biz/inventory/index'
				};
				
				if (routes[type]) {
					this.$tab.navigateTo(routes[type]);
				} else {
					uni.showToast({
						title: '功能开发中...',
						icon: 'none'
					});
				}
			},

			// 更多操作
			handleMoreActions() {
				this.$tab.navigateTo('/pages/biz/index');
			},

			// 任务点击
			handleTaskClick(task) {
				console.log('点击任务:', task);
				// 根据任务类型跳转到对应页面
				this.handleTaskAction(task, 'view');
			},

			// 任务操作
			handleTaskAction(task, action) {
				const routes = {
					inventory: '/pages/biz/inventory/warning',
					order: '/pages/biz/sale/detail',
					approval: '/pages/biz/approval/index'
				};

				if (routes[task.type]) {
					this.$tab.navigateTo(routes[task.type]);
				} else {
					uni.showToast({
						title: '功能开发中...',
						icon: 'none'
					});
				}
			},

			// 查看全部任务
			handleViewAllTasks() {
				this.$tab.navigateTo('/pages/biz/tasks/index');
			},

			// 功能模块点击
			handleModuleClick(module) {
				if (module.route) {
					this.$tab.navigateTo(module.route);
				} else {
					uni.showToast({
						title: '功能开发中...',
						icon: 'none'
					});
				}
			},

			// 全部模块
			handleAllModules() {
				this.$tab.navigateTo('/pages/biz/modules/index');
			},

			// ========== 图表相关方法 ==========

			// 加载图表数据
			async loadChartData() {
				try {
					// 获取最近N天的统计数据
					const days = parseInt(this.salesPeriod);
					const params = {
						pageNo: 1,
						pageSize: days
					};

					const result = await getDailyOrderPageApi(params);
					console.log('获取图表数据接口返回:', result);
					
					if (result && result.data && result.data.list && result.data.list.length > 0) {
						console.log('图表数据列表:', result.data.list);
						// 处理返回的图表数据
						this.processChartData(result.data.list);
					} else {
						// 使用模拟数据
						this.generateMockChartData();
					}
				} catch (error) {
					console.error('获取图表数据失败:', error);
					// 使用模拟数据
					this.generateMockChartData();
				}
			},

			// 处理图表数据
			processChartData(dataList) {
				// 处理统计数据用于图表展示
				if (dataList && Array.isArray(dataList)) {
					// 处理销售图表数据
					this.chartData.sales = dataList.reverse().map(item => ({
						date: item.date ? item.date.substring(5) : '', // 只显示月-日
						amount: item.saleAmount || 0,
						count: item.saleCount || 0
					}));
					
					// 处理生产图表数据
					this.chartData.production = dataList.map(item => ({
						date: item.date ? item.date.substring(5) : '',
						quantity: item.mfgQuantity || 0,
						qualityRate: 98.5 // 模拟合格率
					}));

					// 更新图表汇总数据 - 基于真实数据计算
					this.updateChartSummary(dataList);
				} else {
					this.generateMockChartData();
				}
			},

			// 更新图表汇总数据
			updateChartSummary(dataList) {
				if (!dataList || dataList.length === 0) return;

				// 计算总销售额和订单数
				const totalSales = dataList.reduce((sum, item) => sum + (item.saleAmount || 0), 0);
				const totalOrders = dataList.reduce((sum, item) => sum + (item.saleCount || 0), 0);
				const totalProduction = dataList.reduce((sum, item) => sum + (item.mfgQuantity || 0), 0);

				// 计算增长率（最新一天与前一天的比较）
				let growthRate = 0;
				if (dataList.length >= 2) {
					const latestSale = dataList[dataList.length - 1].saleAmount || 0;
					const previousSale = dataList[dataList.length - 2].saleAmount || 0;
					if (previousSale > 0) {
						growthRate = ((latestSale - previousSale) / previousSale * 100).toFixed(1);
					}
				}

				// 更新图表汇总数据
				this.chartSummary = {
					totalSales: totalSales,
					totalOrders: totalOrders,
					growthRate: parseFloat(growthRate),
					totalProduction: totalProduction,
					qualityRate: 98.6, // 保持模拟数据
					completionRate: 92.3 // 保持模拟数据
				};

				console.log('图表汇总数据已更新:', this.chartSummary);
			},

			// 生成模拟图表数据 - 基于真实数据格式
			generateMockChartData() {
				const days = parseInt(this.salesPeriod);
				
				// 使用真实数据作为基础模板
				const realDataTemplate = [
					{
						date: '07-17',
						saleAmount: 800000,
						saleCount: 10,
						mfgQuantity: 40,
						qualityRate: 98.5
					},
					{
						date: '07-16', 
						saleAmount: 7000000,
						saleCount: 10,
						mfgQuantity: 90,
						qualityRate: 98.6
					}
				];
				
				const salesData = [];
				const productionData = [];
				
				for (let i = days - 1; i >= 0; i--) {
					const date = new Date();
					date.setDate(date.getDate() - i);
					const dateStr = `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
					
					// 如果有真实数据模板，使用它；否则生成基于真实数据范围的随机数据
					let saleAmount, saleCount, mfgQuantity;
					if (i < realDataTemplate.length) {
						const template = realDataTemplate[realDataTemplate.length - 1 - i];
						saleAmount = template.saleAmount;
						saleCount = template.saleCount;
						mfgQuantity = template.mfgQuantity;
					} else {
						// 基于真实数据范围生成
						saleAmount = Math.floor(Math.random() * 6200000) + 800000; // 80万-700万范围
						saleCount = Math.floor(Math.random() * 5) + 8; // 8-12个订单
						mfgQuantity = Math.floor(Math.random() * 60) + 30; // 30-90数量
					}
					
					// 销售数据
					salesData.push({
						date: dateStr,
						amount: saleAmount,
						count: saleCount
					});
					
					// 生产数据
					productionData.push({
						date: dateStr,
						quantity: mfgQuantity,
						qualityRate: (Math.random() * 2 + 97.5).toFixed(1) // 97.5%-99.5%
					});
				}
				
				this.chartData.sales = salesData;
				this.chartData.production = productionData;
			},

			// 初始化图表
			initCharts() {
				// 使用lime-echart组件时，图表会在@finished事件中自动初始化
				// 这里只需要确保数据已经准备好
				console.log('图表数据已准备完成，等待组件初始化');
			},

			// 数据加载完成后初始化图表
			initChartsAfterDataLoaded() {
				console.log('开始初始化图表，当前激活标签:', this.activeChartTab);

				// 根据当前激活的标签初始化对应的图表
				if (this.activeChartTab === 'sales') {
					this.initSalesChart();
				} else if (this.activeChartTab === 'production') {
					this.initProductionChart();
				}
			},

			// 初始化销售图表
			async initSalesChart() {
				try {
					if (!this.$refs.salesChart) {
						console.log('销售图表组件未找到，可能还未渲染完成');
						return;
					}

					// 检查图表数据是否已准备好
					if (!this.chartData.sales || this.chartData.sales.length === 0) {
						console.log('销售图表数据未准备好');
						return;
					}

					console.log('开始初始化销售图表，数据长度:', this.chartData.sales.length);
					const chart = await this.$refs.salesChart.init(echarts);
				
				const dates = this.chartData.sales.map(item => item.date);
				const amounts = this.chartData.sales.map(item => item.amount / 10000); // 转换为万元
				
				const option = {
					grid: {
						left: '3%',
						right: '4%',
						bottom: '3%',
						top: '10%',
						containLabel: true
					},
					xAxis: {
						type: 'category',
						data: dates,
						axisLine: {
							lineStyle: {
								color: '#e5e7eb'
							}
						},
						axisTick: {
							show: false
						},
						axisLabel: {
							color: '#6b7280',
							fontSize: 11
						}
					},
					yAxis: {
						type: 'value',
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLabel: {
							color: '#6b7280',
							fontSize: 11
						},
						splitLine: {
							lineStyle: {
								color: '#f3f4f6'
							}
						}
					},
					series: [{
						name: '销售额',
						type: 'line',
						data: amounts,
						smooth: true,
						lineStyle: {
							color: '#165DFF',
							width: 3
						},
						areaStyle: {
							color: {
								type: 'linear',
								x: 0,
								y: 0,
								x2: 0,
								y2: 1,
								colorStops: [{
									offset: 0,
									color: 'rgba(22, 93, 255, 0.3)'
								}, {
									offset: 1,
									color: 'rgba(22, 93, 255, 0.05)'
								}]
							}
						},
						itemStyle: {
							color: '#165DFF',
							borderWidth: 2,
							borderColor: '#fff'
						},
						emphasis: {
							itemStyle: {
								borderWidth: 3,
								borderColor: '#165DFF',
								scale: 1.2
							}
						}
					}],
					tooltip: {
						trigger: 'axis',
						backgroundColor: 'rgba(255, 255, 255, 0.95)',
						borderColor: '#e5e7eb',
						borderWidth: 1,
						textStyle: {
							color: '#374151'
						},
						formatter: function(params) {
							const data = params[0];
							return `${data.axisValue}<br/>销售额: ${data.value.toFixed(1)}万元`;
						}
					}
				};
				
					chart.setOption(option);
					console.log('销售图表初始化完成');
				} catch (error) {
					console.error('销售图表初始化失败:', error);
				}
			},

			// 初始化生产图表
			async initProductionChart() {
				try {
					if (!this.$refs.productionChart) {
						console.log('生产图表组件未找到，可能还未渲染完成');
						return;
					}

					// 检查图表数据是否已准备好
					if (!this.chartData.production || this.chartData.production.length === 0) {
						console.log('生产图表数据未准备好');
						return;
					}

					console.log('开始初始化生产图表，数据长度:', this.chartData.production.length);
					const chart = await this.$refs.productionChart.init(echarts);
				
				const dates = this.chartData.production.map(item => item.date);
				const quantities = this.chartData.production.map(item => item.quantity);
				
				const option = {
					grid: {
						left: '3%',
						right: '4%',
						bottom: '3%',
						top: '10%',
						containLabel: true
					},
					xAxis: {
						type: 'category',
						data: dates,
						axisLine: {
							lineStyle: {
								color: '#e5e7eb'
							}
						},
						axisTick: {
							show: false
						},
						axisLabel: {
							color: '#6b7280',
							fontSize: 11
						}
					},
					yAxis: {
						type: 'value',
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLabel: {
							color: '#6b7280',
							fontSize: 11
						},
						splitLine: {
							lineStyle: {
								color: '#f3f4f6'
							}
						}
					},
					series: [{
						name: '产量',
						type: 'bar',
						data: quantities,
						itemStyle: {
							color: {
								type: 'linear',
								x: 0,
								y: 0,
								x2: 0,
								y2: 1,
								colorStops: [{
									offset: 0,
									color: '#00B42A'
								}, {
									offset: 1,
									color: '#52C41A'
								}]
							},
							borderRadius: [4, 4, 0, 0]
						},
						barWidth: '60%',
						emphasis: {
							itemStyle: {
								shadowBlur: 10,
								shadowColor: 'rgba(0, 180, 42, 0.3)'
							}
						}
					}],
					tooltip: {
						trigger: 'axis',
						backgroundColor: 'rgba(255, 255, 255, 0.95)',
						borderColor: '#e5e7eb',
						borderWidth: 1,
						textStyle: {
							color: '#374151'
						},
						formatter: function(params) {
							const data = params[0];
							return `${data.axisValue}<br/>产量: ${data.value}吨`;
						}
					}
				};
				
					chart.setOption(option);
					console.log('生产图表初始化完成');
				} catch (error) {
					console.error('生产图表初始化失败:', error);
				}
			},

			// 切换图表标签
			switchChartTab(tab) {
				this.activeChartTab = tab;
				// 延迟初始化对应的图表
				this.$nextTick(() => {
					setTimeout(() => {
						if (tab === 'sales') {
							this.initSalesChart();
						} else if (tab === 'production') {
							this.initProductionChart();
						}
					}, 100);
				});
			},

			// 改变销售数据周期
			changeSalesPeriod(period) {
				this.salesPeriod = period;
				this.loadChartData().then(() => {
					this.initSalesChart();
				});
			},

			// 改变生产数据周期
			changeProductionPeriod(period) {
				this.productionPeriod = period;
				this.loadChartData().then(() => {
					this.initProductionChart();
				});
			}
		}
	}
</script>

<style lang="scss">
	// 主容器样式
	.scm-home-container {
		width: 100%;
		min-height: 100vh;
		background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
		padding-bottom: 20px;
	}

	// 欢迎区域样式
	.welcome-section {
		padding: 20px 16px;
		
		.welcome-card {
			background: white;
			border-radius: 16px;
			padding: 20px 16px;
			color: #1f2937;
			box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
			position: relative;
			overflow: hidden;

			// 右上角1/4圆装饰
			&::before {
				content: '';
				position: absolute;
				top: 0;
				right: 0;
				width: 60px;
				height: 60px;
				background: #f8fafc;
				border-radius: 0 0 0 100%;
			}

			// 左下角1/4圆装饰
			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				width: 50px;
				height: 50px;
				background: #f1f5f9;
				border-radius: 0 100% 0 0;
			}
		}
		
		.welcome-header {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 20px;
			position: relative;
			z-index: 2;
			
			.greeting {
				flex: 1;

				.greeting-text {
					font-size: 20px;
					font-weight: bold;
					display: block;
					margin-bottom: 8px;
				}

				.date-info {
					display: block;

					.date-text {
						font-size: 14px;
						color: #4b5563;
						font-weight: 500;
						display: inline;
					}

					.task-info {
						font-size: 13px;
						color: #6b7280;
						display: inline;

						.task-count {
							font-size: 14px;
							font-weight: bold;
							color: #f59e0b;
							background: rgba(245, 158, 11, 0.1);
							padding: 2px 6px;
							border-radius: 4px;
							margin: 0 2px;
						}
					}
				}
			}
			
			.avatar-section {
				.user-avatar {
					width: 48px;
					height: 48px;
					border-radius: 50%;
					border: 2px solid rgba(255, 255, 255, 0.3);
				}
			}
		}
		
		.status-cards {
			display: flex;
			justify-content: space-between;
			gap: 8px;
			position: relative;
			z-index: 2;
			
			.status-item {
		flex: 1;
				border-radius: 12px;
				padding: 12px 10px;
		display: flex;
				align-items: center;
				transition: all 0.3s ease;
				min-width: 0; // 允许flex子项收缩

				// 默认背景
				background: #f8fafc;

				// 根据不同状态设置不同的背景色
				&:nth-child(1) {
					background: rgba(255, 125, 0, 0.05);
					border: 1px solid rgba(255, 125, 0, 0.08);
				}

				&:nth-child(2) {
					background: rgba(245, 63, 63, 0.05);
					border: 1px solid rgba(245, 63, 63, 0.08);
				}

				&:nth-child(3) {
					background: rgba(0, 180, 42, 0.05);
					border: 1px solid rgba(0, 180, 42, 0.08);
				}

				&:active {
					transform: scale(0.98);
				}
				
				.status-icon {
					width: 30px;
					height: 30px;
					border-radius: 8px;
					display: flex;
		align-items: center;
		justify-content: center;
					margin-right: 10px;
					font-size: 15px;
					box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
					flex-shrink: 0; // 防止图标被压缩

					&.pending-orders {
						background: linear-gradient(135deg, #FF7D00, #FFA940);
						color: white;
					}

					&.inventory-warning {
						background: linear-gradient(135deg, #F53F3F, #FF7875);
						color: white;
					}

					&.approval-pending {
						background: linear-gradient(135deg, #00B42A, #52C41A);
						color: white;
					}
				}
				
				.status-content {
		flex: 1;
					min-width: 0; // 允许内容收缩

					.status-label {
						font-size: 10px;
						color: #6b7280;
						display: block;
						margin-bottom: 2px;
						font-weight: 500;
						line-height: 1.2;
					}

					.status-value {
						font-size: 13px;
						font-weight: bold;
						color: #1f2937;
						display: block;
						line-height: 1.2;
					}
				}
			}
		}
	}

	// 业务数据统计卡片样式
	.metrics-section {
		padding: 0 16px 20px;
		
		.metrics-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 12px;
		}
		
		.metric-card {
			background: white;
			border-radius: 12px;
			padding: 16px;
			box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
			transition: all 0.3s ease;
			
			&:active {
				transform: scale(0.98);
			}
			
			.metric-header {
		display: flex;
				align-items: center;
				margin-bottom: 12px;
				
				.metric-icon {
					width: 36px;
					height: 36px;
					border-radius: 10px;
					display: flex;
		align-items: center;
		justify-content: center;
					margin-right: 12px;
					font-size: 18px;
					color: white;
					
					&.sales-icon {
						background: linear-gradient(135deg, #6366f1, #8b5cf6);
					}
					
					&.outbound-icon {
						background: linear-gradient(135deg, #3b82f6, #2563eb);
					}
					
					&.purchase-icon {
						background: linear-gradient(135deg, #0ea5e9, #06b6d4);
					}
					
					&.production-icon {
						background: linear-gradient(135deg, #10b981, #059669);
					}
				}
				
				.metric-info {
					flex: 1;
					
					.metric-title {
						font-size: 14px;
						font-weight: 600;
						color: #1f2937;
						display: block;
						margin-bottom: 2px;
					}
					
					.metric-tag {
						font-size: 11px;
						color: #6b7280;
						background: #f3f4f6;
						padding: 2px 6px;
						border-radius: 4px;
						display: inline-block;
					}
				}
			}
			
			.metric-content {
				.metric-amount {
					font-size: 20px;
					font-weight: bold;
					color: #1f2937;
					display: block;
					margin-bottom: 4px;
				}
				
				.metric-count {
					font-size: 13px;
					color: #6b7280;
					display: block;
				}
			}
		}
	}

	// 快捷操作区域样式
	.quick-actions-section {
		padding: 0 16px 20px;
		
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			
			.section-title {
				font-size: 16px;
				font-weight: 600;
				color: #1f2937;
			}
			
			.section-more {
				font-size: 14px;
				color: #165DFF;
			}
		}
		
		.quick-actions-grid {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: 12px;
		}
		
		.quick-action-item {
			background: white;
			border-radius: 12px;
			padding: 16px 8px;
		display: flex;
		flex-direction: column;
		align-items: center;
			box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
			transition: all 0.3s ease;
			
			&:active {
				transform: translateY(-2px);
				box-shadow: 0 4px 20px rgba(22, 93, 255, 0.15);
			}
			
			.action-icon {
				width: 40px;
				height: 40px;
				border-radius: 12px;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 8px;
				font-size: 20px;
				color: white;
				
				&.sales-bg {
					background: linear-gradient(135deg, #165DFF, #36BFFA);
				}
				
				&.purchase-bg {
					background: linear-gradient(135deg, #36BFFA, #00D4FF);
				}
				
				&.production-bg {
					background: linear-gradient(135deg, #00B42A, #52C41A);
				}
				
				&.inventory-bg {
					background: linear-gradient(135deg, #FF7D00, #FFA940);
				}
			}
			
			.action-text {
				font-size: 12px;
				color: #1f2937;
				font-weight: 500;
				text-align: center;
			}
		}
	}

	// 待处理任务样式
	.pending-tasks-section {
		padding: 0 16px 20px;
		
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			
			.section-title {
				font-size: 16px;
				font-weight: 600;
				color: #1f2937;
			}
			
			.section-more {
				font-size: 14px;
				color: #165DFF;
			}
		}
		
		.pending-tasks-list {
			background: white;
			border-radius: 12px;
			overflow: hidden;
			box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
		}
		
		.task-item {
			display: flex;
			align-items: flex-start;
			padding: 16px;
			border-bottom: 1px solid #f1f5f9;
			transition: background-color 0.2s;
			
			&:last-child {
				border-bottom: none;
			}
			
			&:active {
				background-color: #f8fafc;
			}
			
			.task-icon {
				width: 36px;
				height: 36px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 12px;
				font-size: 16px;
				flex-shrink: 0;
				
				&.task-icon-warning {
					background: rgba(245, 63, 63, 0.1);
					color: #F53F3F;
				}
				
				&.task-icon-info {
					background: rgba(255, 125, 0, 0.1);
					color: #FF7D00;
				}
				
				&.task-icon-success {
					background: rgba(22, 93, 255, 0.1);
					color: #165DFF;
				}
			}
			
			.task-content {
				flex: 1;
				margin-right: 12px;
				
				.task-title {
					font-size: 14px;
					font-weight: 600;
					color: #1f2937;
					display: block;
					margin-bottom: 4px;
				}
				
				.task-desc {
					font-size: 13px;
					color: #6b7280;
					display: block;
					line-height: 1.4;
					margin-bottom: 6px;
				}
				
				.task-time {
					font-size: 11px;
					color: #9ca3af;
					display: block;
				}
			}
			
			.task-actions {
				flex-shrink: 0;
				
				.task-btn {
					padding: 6px 12px;
					border-radius: 6px;
					font-size: 12px;
					font-weight: 500;
					
					&.primary {
						background: rgba(22, 93, 255, 0.1);
						color: #165DFF;
					}
				}
			}
		}
	}

	// 图表区域样式
	.charts-section {
		padding: 0 16px 20px;
		
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			
			.section-title {
				font-size: 16px;
				font-weight: 600;
				color: #1f2937;
			}
			
			.chart-tabs {
				display: flex;
				background: #f3f4f6;
				border-radius: 8px;
				padding: 2px;
				
				.chart-tab {
					padding: 6px 12px;
					font-size: 12px;
					border-radius: 6px;
					color: #6b7280;
					transition: all 0.2s;
					
					&.active {
						background: #165DFF;
						color: white;
					}
				}
			}
		}
		
		.chart-container {
			.chart-card {
				background: white;
				border-radius: 12px;
				padding: 16px;
				box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
				
				.chart-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 16px;
					
					.chart-title {
						font-size: 14px;
						font-weight: 600;
						color: #1f2937;
					}
					
					.chart-period-tabs {
						display: flex;
						background: #f3f4f6;
						border-radius: 6px;
						padding: 2px;
						
						.period-tab {
							padding: 4px 8px;
							font-size: 11px;
							border-radius: 4px;
							color: #6b7280;
							transition: all 0.2s;
							
							&.active {
								background: #165DFF;
								color: white;
							}
						}
					}
				}
				
				.chart-content {
					.chart-canvas {
						width: 100%;
						height: 200px;
					}
				}
				
				.chart-summary {
					display: flex;
					justify-content: space-around;
					margin-top: 16px;
					padding-top: 16px;
					border-top: 1px solid #f1f5f9;
					
					.summary-item {
						text-align: center;
						
						.summary-label {
							font-size: 11px;
							color: #6b7280;
						display: block;
							margin-bottom: 4px;
						}
						
						.summary-value {
							font-size: 16px;
							font-weight: bold;
							color: #1f2937;
							display: block;
							
							&.growth-positive {
								color: #00B42A;
							}
							
							&.growth-negative {
								color: #F53F3F;
							}
						}
					}
				}
			}
		}
	}

	// 功能模块样式
	.function-modules-section {
		padding: 0 16px 20px;
		
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			
			.section-title {
				font-size: 16px;
				font-weight: 600;
				color: #1f2937;
			}
			
			.section-more {
				font-size: 14px;
				color: #165DFF;
			}
		}
		
		.modules-grid {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: 12px;
		}
		
		.module-item {
			background: white;
			border-radius: 12px;
			padding: 16px 8px;
			display: flex;
			flex-direction: column;
			align-items: center;
			box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
			transition: all 0.3s ease;
			
			&:active {
				transform: translateY(-2px);
				box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
			}
			
			.module-icon {
				width: 36px;
				height: 36px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 8px;
				font-size: 16px;
				
				&.module-purple {
					background: #f3e8ff;
					color: #8b5cf6;
				}
				
				&.module-green {
					background: #dcfce7;
					color: #22c55e;
				}
				
				&.module-blue {
					background: #dbeafe;
					color: #3b82f6;
				}
				
				&.module-orange {
					background: #fed7aa;
					color: #f97316;
				}
				
				&.module-teal {
					background: #ccfbf1;
					color: #14b8a6;
				}
				
				&.module-indigo {
					background: #e0e7ff;
					color: #6366f1;
				}
				
				&.module-pink {
					background: #fce7f3;
					color: #ec4899;
				}
				
				&.module-gray {
					background: #f3f4f6;
					color: #6b7280;
				}
			}
			
			.module-text {
				font-size: 12px;
				color: #1f2937;
				font-weight: 500;
				text-align: center;
			}
		}
	}

	// 响应式设计
	@media screen and (min-width: 768px) {
		.scm-home-container {
			max-width: 768px;
			margin: 0 auto;
		}
		
		.metrics-grid {
			grid-template-columns: repeat(4, 1fr) !important;
		}
		
		.quick-actions-grid {
			grid-template-columns: repeat(8, 1fr) !important;
		}
		
		.modules-grid {
			grid-template-columns: repeat(8, 1fr) !important;
		}
	}
</style>